// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.VisionMaster.NodeData;
using H.VisionMaster.NodeGroup;
using H.VisionMaster.NodeGroup.Groups.Preprocessings;
using H.VisionMaster.OpenCV.Base;
using OpenCvSharp;

namespace H.VisionMaster.OpenCV.NodeDatas.Basic;

/// <summary>
/// 示例插件：图像锐化
/// 演示如何使用 VisionPluginGroupNames 常量类来避免分组名称拼写错误
/// </summary>
[Icon(FontIcons.Brightness)]
//[Display(
//    Name = "图像锐化示例", 
//    GroupName = "特征匹配",  // 使用常量类，避免拼写错误
//    Description = "使用卷积核增强图像边缘和细节，演示标准化分组使用", 
//    Order = 25
//)]
[Display(Name = "图像锐化示例条件分支", GroupName = "判断条件", Description = "使用卷积核增强图像边缘和细节，演示标准化分组使用", Order = 30)]
public class ExampleSharpenNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    #region 插件参数

    private double _strength = 1.0;
    /// <summary>
    /// 锐化强度
    /// </summary>
    [Display(Name = "锐化强度", GroupName = VisionPropertyGroupNames.RunParameters)]
    [Range(0.1, 3.0)]
    public double Strength
    {
        get { return _strength; }
        set
        {
            _strength = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent(); // 实时预览
        }
    }

    private SharpenKernelType _kernelType = SharpenKernelType.Standard;
    /// <summary>
    /// 锐化核类型
    /// </summary>
    [Display(Name = "锐化核类型", GroupName = VisionPropertyGroupNames.RunParameters)]
    public SharpenKernelType KernelType
    {
        get { return _kernelType; }
        set
        {
            _kernelType = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    #endregion

    #region 核心处理方法

    protected override FlowableResult<Mat> Invoke(
        ISrcVisionNodeData<Mat> srcImageNodeData, 
        IVisionNodeData<Mat> from, 
        IFlowableDiagramData diagram)
    {
        try
        {
            Mat input = from.Mat;
            if (input?.Empty() != false)
                return this.Error(null, "输入图像无效");

            // 根据选择的核类型创建锐化核
            Mat kernel = CreateSharpenKernel(_kernelType, _strength);
            
            Mat output = new Mat();
            Cv2.Filter2D(input, output, -1, kernel);
            
            // 释放临时资源
            kernel.Dispose();
            
            return this.OK(output, $"锐化处理完成 - 类型: {_kernelType}, 强度: {_strength:F1}");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"锐化处理失败: {ex.Message}");
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 创建锐化卷积核
    /// </summary>
    /// <param name="kernelType">核类型</param>
    /// <param name="strength">强度</param>
    /// <returns>锐化核</returns>
    private Mat CreateSharpenKernel(SharpenKernelType kernelType, double strength)
    {
        float s = (float)strength;
        
        return kernelType switch
        {
            SharpenKernelType.Standard => new Mat(3, 3, MatType.CV_32F, new float[]
            {
                0, -1 * s, 0,
                -1 * s, 1 + 4 * s, -1 * s,
                0, -1 * s, 0
            }),
            
            SharpenKernelType.Strong => new Mat(3, 3, MatType.CV_32F, new float[]
            {
                -1 * s, -1 * s, -1 * s,
                -1 * s, 1 + 8 * s, -1 * s,
                -1 * s, -1 * s, -1 * s
            }),
            
            SharpenKernelType.Edge => new Mat(3, 3, MatType.CV_32F, new float[]
            {
                0, -1 * s, 0,
                -1 * s, 1 + 4 * s, -1 * s,
                0, -1 * s, 0
            }),
            
            SharpenKernelType.Laplacian => new Mat(3, 3, MatType.CV_32F, new float[]
            {
                0, 1 * s, 0,
                1 * s, 1 - 4 * s, 1 * s,
                0, 1 * s, 0
            }),
            
            _ => new Mat(3, 3, MatType.CV_32F, new float[]
            {
                0, -1 * s, 0,
                -1 * s, 1 + 4 * s, -1 * s,
                0, -1 * s, 0
            })
        };
    }

    #endregion

    #region 插件信息重写

    public override string ToString()
    {
        return $"图像锐化示例 (强度: {_strength:F1}, 类型: {_kernelType})";
    }

    #endregion
}

/// <summary>
/// 锐化核类型枚举
/// </summary>
public enum SharpenKernelType
{
    [Display(Name = "标准锐化", Description = "标准的4邻域锐化核")]
    Standard,
    
    [Display(Name = "强锐化", Description = "8邻域强锐化核")]
    Strong,
    
    [Display(Name = "边缘锐化", Description = "专注于边缘的锐化核")]
    Edge,
    
    [Display(Name = "拉普拉斯", Description = "拉普拉斯锐化核")]
    Laplacian
}

/*
使用说明：

1. 分组名称使用：
   - 使用 VisionPluginGroupNames.ImageEnhancement 常量
   - 避免手动输入 "图像增强" 字符串
   - 编译时检查，防止拼写错误

2. 参数分组使用：
   - 使用 VisionPropertyGroupNames.RunParameters 常量
   - 保持与现有插件的一致性

3. 开发建议：
   - 优先使用 VisionPluginGroupNames 中的常量
   - 如果需要新的分组，先在 VisionPluginGroupNames 中添加
   - 使用 VisionPluginGroupHelper.GetRecommendedGroups() 获取推荐分组

4. 验证方法：
   var result = VisionPluginGroupHelper.ValidatePluginGroup(typeof(ExampleSharpenNodeData));
   if (!result.IsValid) 
   {
       Console.WriteLine($"分组配置错误: {result.ErrorMessage}");
   }
*/
