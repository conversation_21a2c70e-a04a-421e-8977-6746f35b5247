# OpenCV插件架构技术文档

## 📋 目录
- [1. 项目结构概览](#1-项目结构概览)
- [2. 插件设计原理](#2-插件设计原理)
- [3. 插件清单](#3-插件清单)
- [4. 插件分类体系](#4-插件分类体系)
- [5. 开发指南](#5-开发指南)
- [6. 代码示例](#6-代码示例)

---

## 1. 项目结构概览

### 1.1 目录结构

```
Source\VisionMaster\H.VisionMaster.OpenCV\
├── Base\                           # 基础类和接口
│   ├── IOpenCVNodeData.cs         # 核心插件接口
│   ├── OpenCVNodeDataBase.cs      # 插件基类
│   ├── FeatureOpenCVNodeDataBase.cs
│   ├── MorphologyOpenCVNodeDataBase.cs
│   └── ...
├── NodeDatas\                      # 插件实现
│   ├── 1 - Src\                   # 数据源插件
│   ├── 2 - Preprocessings\        # 预处理插件
│   ├── 3 - Blurs\                 # 滤波插件
│   ├── 3 - Takeoffs\              # 提取插件
│   ├── 4 - Morphology\            # 形态学插件
│   ├── 5 - Conditions\            # 条件判断插件
│   ├── 6 - TemplateMatchings\     # 模板匹配插件
│   ├── 7 - Detector\              # 检测器插件
│   ├── 8 - Feature\               # 特征检测插件
│   ├── 9 - Other\                 # 其他功能插件
│   ├── 9 - Outputs\               # 输出插件
│   └── Video\                     # 视频处理插件
├── NodeDataGroups\                 # 插件分组管理
│   ├── OpenCVPreprocessingsDataGroup.cs
│   ├── FeatureDetectorDataGroup.cs
│   └── ...
├── Data\                          # 数据文件
├── Themes\                        # 主题资源
└── TypeConverters\                # 类型转换器
```

### 1.2 文件组织原则

1. **按功能分类**：插件按照图像处理流程的不同阶段进行分组
2. **数字前缀排序**：使用数字前缀确保处理流程的逻辑顺序
3. **基类分离**：将通用基类和接口放在 Base 目录
4. **分组管理**：使用 NodeDataGroups 管理插件的自动发现和注册

---

## 2. 插件设计原理

### 2.1 核心架构

#### 继承层次结构
```
NodeDataBase (WPF-Control基础)
    ↓ DisplayBindableBase, ICloneable, INodeData
DemoNodeDataBase 
    ↓ HelpNodeDataBase (帮助系统)
VisionNodeData<T> 
    ↓ 核心抽象方法: Invoke(ISrcVisionNodeData<T>, IVisionNodeData<T>, IFlowableDiagramData)
ROINodeData<T>
    ↓ 添加ROI(感兴趣区域)支持
SelectableResultImageNodeData<T>
    ↓ 添加图像选择和结果展示
OpenCVNodeDataBase
    ↓ OpenCV特定实现
```

#### 核心接口设计

**IOpenCVNodeData 接口**：
```csharp
public interface IOpenCVNodeData : IVisionNodeData<Mat>
{
    // 标记接口，继承自 IVisionNodeData<Mat>
}
```

**OpenCVNodeDataBase 基类**：
```csharp
public abstract class OpenCVNodeDataBase : SelectableResultImageNodeData<Mat>, IOpenCVNodeData
{
    protected override bool IsValid(Mat t) => t.IsValid();
    protected override void UpdateResultImageSource() => this.UpdateResultImageSource(this.Mat);
    protected virtual void UpdateResultImageSource(Mat mat) => this.ResultImageSource = mat.ToImageSource();
}
```

### 2.2 插件生命周期管理

1. **发现阶段**：应用启动时通过反射扫描程序集
2. **注册阶段**：按接口类型自动分组注册到工具箱
3. **实例化阶段**：用户拖拽时创建插件实例
4. **执行阶段**：按连接顺序调用 Invoke() 方法
5. **销毁阶段**：处理完成后释放资源

### 2.3 插件发现机制

**核心扩展方法**：
```csharp
public static IEnumerable<T> GetInstances<T>(this Assembly assembly)
{
    var types = assembly.GetTypes();
    types = types.Where(t => t.IsClass && !t.IsAbstract).ToArray();
    types = types.Where(t => typeof(T).IsAssignableFrom(t)).ToArray();
    return types.Select(t => Activator.CreateInstance(t)).OfType<T>();
}
```

**分组注册机制**：
```csharp
protected override IEnumerable<INodeData> CreateNodeDatas()
{
    return this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>().OrderBy(x => x.Order);
}
```

### 2.4 设计模式分析

#### 工厂模式
- **实现位置**：Assembly.GetInstances<T>() 扩展方法
- **作用**：通过反射自动创建插件实例
- **优势**：无需手动注册，支持动态发现

#### 模板方法模式
- **实现位置**：OpenCVNodeDataBase 基类
- **核心方法**：Invoke() 抽象方法
- **作用**：定义插件执行的标准流程

#### 策略模式
- **实现位置**：不同的插件实现类
- **作用**：每个插件代表一种图像处理策略
- **优势**：算法可替换，易于扩展

#### 观察者模式
- **实现位置**：属性变更通知 (INotifyPropertyChanged)
- **作用**：参数变更时自动更新预览
- **实现**：RaisePropertyChanged() 和 UpdateInvokeCurrent()

#### 依赖注入模式
- **实现位置**：插件加载服务
- **接口**：IPluginLoaderService
- **作用**：支持动态插件加载和管理

---

## 3. 插件清单

### 3.1 数据源插件 (1 - Src)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| 本地图像源 | SrcImageFilesNodeData | 加载本地图像文件 | ISrcImageGroupableNodeData |
| 摄像头采集 | CameraCaptureNodeData | 实时摄像头图像采集 | ICameraCaptureNodeData |
| 视频文件源 | SrcVideoFilesNodeData | 加载视频文件 | ISrcImageGroupableNodeData |

### 3.2 预处理插件 (2 - Preprocessings)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| 色彩变换 | CvtColor | 颜色空间转换 | IPreprocessingGroupableNodeData |
| 图像翻转 | Flip | 水平/垂直翻转 | IPreprocessingGroupableNodeData |
| 反转黑白 | BitwiseNot | 二值图像反转 | IPreprocessingGroupableNodeData |
| 加减运算 | AddSutract | 图像加减法运算 | IPreprocessingGroupableNodeData |
| 乘除运算 | MultiplayDivide | 图像乘除法运算 | IPreprocessingGroupableNodeData |
| 图像缩放 | Resize | 改变图像尺寸 | IPreprocessingGroupableNodeData |
| 图像旋转 | Rotate | 任意角度旋转 | IPreprocessingGroupableNodeData |
| 阈值处理 | Threshold | 二值化处理 | IPreprocessingGroupableNodeData |
| 通道分割 | SplitBGR | BGR通道分离 | IPreprocessingGroupableNodeData |
| 归一化 | Normalize | 像素值归一化 | IPreprocessingGroupableNodeData |
| 幂运算 | Pow | 像素值幂运算 | IPreprocessingGroupableNodeData |
| 重复 | Repeat | 图像重复平铺 | IPreprocessingGroupableNodeData |

### 3.3 滤波插件 (3 - Blurs)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| 基础滤波 | Blur | 均值滤波 | IBlurGroupableNodeData |
| 高斯滤波 | GaussianBlur | 高斯核滤波 | IBlurGroupableNodeData |
| 细节增强 | DetailEnhance | 保边滤波增强 | IBlurGroupableNodeData |
| 边缘保持滤波 | EdgePreservingFilter | 边缘保持平滑 | IBlurGroupableNodeData |
| 铅笔素描 | PencilSketch | 铅笔画效果 | IBlurGroupableNodeData |
| 风格化 | Stylization | 艺术风格化 | IBlurGroupableNodeData |

### 3.4 提取插件 (3 - Takeoffs)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| 按位与 | BitwiseAnd | 图像按位与运算 | ITakeoffGroupableNodeData |
| HSV范围提取 | HSVInRange | HSV颜色范围提取 | ITakeoffGroupableNodeData |
| 无缝克隆背景 | SeamlessCloneBackground | 背景无缝替换 | ITakeoffGroupableNodeData |

### 3.5 形态学插件 (4 - Morphology)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| 膨胀 | Dilate | 形态学膨胀 | IMorphologyGroupableNodeData |
| 腐蚀 | Erode | 形态学腐蚀 | IMorphologyGroupableNodeData |
| 开运算 | Open | 先腐蚀后膨胀 | IMorphologyGroupableNodeData |
| 闭运算 | Close | 先膨胀后腐蚀 | IMorphologyGroupableNodeData |
| 梯度 | Gradient | 形态学梯度 | IMorphologyGroupableNodeData |
| 顶帽 | TopHat | 顶帽变换 | IMorphologyGroupableNodeData |
| 黑帽 | BlackHat | 黑帽变换 | IMorphologyGroupableNodeData |

### 3.6 条件判断插件 (5 - Conditions)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| OpenCV条件 | OpenCVConditionNodeData | 通用条件判断 | IConditionGroupableNodeData |
| 像素阈值条件 | PixelThresholdIfConditionNodeData | 像素值条件判断 | IConditionGroupableNodeData |

### 3.7 模板匹配插件 (6 - TemplateMatchings)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| 最佳匹配模板 | BestMatchBase64TemplateMatchingNodeData | Base64模板匹配 | ITemplateMatchingGroupableNodeData |
| HSV范围渲染匹配 | HSVInRangeRenderBlobMatchingNodeData | HSV颜色匹配 | ITemplateMatchingGroupableNodeData |
| SIFT特征匹配 | SiftBase64FeatureMatchingNodeData | SIFT特征匹配 | ITemplateMatchingGroupableNodeData |
| SURF特征匹配 | SurfBase64FeatureMatchingNodeData | SURF特征匹配 | ITemplateMatchingGroupableNodeData |
| 模板匹配 | TemplateBase64MatchingNodeData | 传统模板匹配 | ITemplateMatchingGroupableNodeData |

### 3.8 检测器插件 (7 - Detector)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| Blob检测 | BlobDetector | 斑点检测 | IDetectorGroupableNodeData |
| 边缘识别 | Canny | Canny边缘检测 | IDetectorGroupableNodeData |
| 轮廓查找 | FindContours | 轮廓检测 | IDetectorGroupableNodeData |
| 霍夫直线 | HoughLines | 霍夫直线检测 | IDetectorGroupableNodeData |
| 霍夫线段 | HoughLinesP | 概率霍夫线段 | IDetectorGroupableNodeData |
| 二维码识别 | QRCode | QR码检测识别 | IDetectorGroupableNodeData |
| 渲染Blob | RenderBlobs | Blob结果渲染 | IDetectorGroupableNodeData |

### 3.9 特征检测插件 (8 - Feature)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| AKAZE特征 | AKazeFeatureDetector | AKAZE特征检测 | IFeatureDetectorOpenCVNodeData |
| BRISK特征 | BriskFeatureDetector | BRISK特征检测 | IFeatureDetectorOpenCVNodeData |
| FAST特征 | FastFeatureDetector | FAST角点检测 | IFeatureDetectorOpenCVNodeData |
| FREAK特征 | FreakFeatureDetector | FREAK描述子 | IFeatureDetectorOpenCVNodeData |
| 单应性变换 | HomographyTransform | 单应性矩阵变换 | IFeatureDetectorOpenCVNodeData |
| KAZE特征 | KazeFeatureDetector | KAZE特征检测 | IFeatureDetectorOpenCVNodeData |
| MSER特征 | MserFeatureDetector | MSER区域检测 | IFeatureDetectorOpenCVNodeData |
| Star特征 | StarFeatureDetector | Star关键点检测 | IFeatureDetectorOpenCVNodeData |

### 3.10 其他功能插件 (9 - Other)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| DNN超分辨率 | DnnSuperres | 深度学习超分辨率 | IOtherGroupableNodeData |
| Haar级联 | HaarCascade | Haar特征检测 | IOtherGroupableNodeData |
| 直方图 | Hist | 图像直方图计算 | IOtherGroupableNodeData |
| HOG特征 | Hog | HOG特征描述子 | IOtherGroupableNodeData |
| LBP级联 | LbpCascade | LBP特征检测 | IOtherGroupableNodeData |
| 支持向量机 | SVM | SVM分类器 | IOtherGroupableNodeData |
| 无缝克隆 | SeamlessClone | 泊松融合 | IOtherGroupableNodeData |
| 图像拼接 | Stitching | 全景拼接 | IOtherGroupableNodeData |
| 三角剖分 | Subdiv2D | Delaunay三角剖分 | IOtherGroupableNodeData |
| 仿射变换 | WarpAffineTransform | 仿射几何变换 | IOtherGroupableNodeData |
| 透视变换 | WarpPerspectiveTransform | 透视几何变换 | IOtherGroupableNodeData |
| YOLO检测 | Yolov3 | YOLO目标检测 | IOtherGroupableNodeData |

### 3.11 输出插件 (9 - Outputs)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| NG输出 | NGOutputNodeData | 不合格结果输出 | IOutputGroupableNodeData |
| OK输出 | OKOutputNodeData | 合格结果输出 | IOutputGroupableNodeData |
| 对话框消息 | ShowDialogNotifyMessageOutputNodeData | 对话框通知 | IOutputGroupableNodeData |
| 错误消息 | ShowErrorNotifyMessageOutputNodeData | 错误通知 | IOutputGroupableNodeData |
| 致命错误消息 | ShowFatalNotifyMessageOutputNodeData | 致命错误通知 | IOutputGroupableNodeData |
| 信息消息 | ShowInfoNotifyMessageOutputNodeData | 信息通知 | IOutputGroupableNodeData |
| 成功消息 | ShowSuccessNotifyMessageOutputNodeData | 成功通知 | IOutputGroupableNodeData |
| 警告消息 | ShowWarnNotifyMessageOutputNodeData | 警告通知 | IOutputGroupableNodeData |

### 3.12 视频处理插件 (Video)
| 插件名称 | 类名 | 功能描述 | 接口 |
|---------|------|----------|------|
| MOG背景建模 | MOG | 混合高斯背景建模 | IVideoGroupableNodeData |
| 视频写入 | VideoWriter | 视频文件写入 | IVideoGroupableNodeData |

**插件总计**：约 80+ 个插件，覆盖完整的计算机视觉处理流程。

---

## 4. 插件分类体系

### 4.1 分类标准

插件分类基于以下几个维度：

1. **功能维度**：按照图像处理的不同阶段和功能进行分类
2. **接口维度**：通过实现特定的分组接口进行归类
3. **目录维度**：物理文件组织与逻辑分类保持一致
4. **执行顺序**：按照典型的图像处理流程进行排序

### 4.2 分组接口体系

| 分组接口 | 插件数量 | 功能描述 | Order范围 |
|----------|----------|----------|-----------|
| `ISrcImageGroupableNodeData` | 3+ | 图像数据源 | 10000-10099 |
| `IPreprocessingGroupableNodeData` | 12+ | 图像预处理 | 10100-10199 |
| `IBlurGroupableNodeData` | 6+ | 滤波模糊 | 10200-10299 |
| `ITakeoffGroupableNodeData` | 3+ | 图像提取 | 10300-10399 |
| `IMorphologyGroupableNodeData` | 7+ | 形态学操作 | 10400-10499 |
| `IConditionGroupableNodeData` | 2+ | 条件判断 | 10500-10599 |
| `ITemplateMatchingGroupableNodeData` | 5+ | 模板匹配 | 10600-10699 |
| `IDetectorGroupableNodeData` | 7+ | 对象检测 | 10700-10799 |
| `IFeatureDetectorOpenCVNodeData` | 8+ | 特征检测 | 10800-10899 |
| `IOtherGroupableNodeData` | 12+ | 其他功能 | 10900-10999 |
| `IOutputGroupableNodeData` | 8+ | 结果输出 | 11000-11099 |
| `IVideoGroupableNodeData` | 2+ | 视频处理 | 11100-11199 |

### 4.3 分类判断依据

#### 4.3.1 功能性分类
- **数据源类**：负责图像输入和采集
- **预处理类**：基础的像素级操作和变换
- **滤波类**：噪声去除和图像平滑
- **形态学类**：基于结构元素的形状操作
- **检测类**：特定目标或特征的识别
- **输出类**：结果展示和数据输出

#### 4.3.2 技术性分类
- **传统CV算法**：基于OpenCV经典算法
- **深度学习算法**：基于神经网络的方法
- **几何变换**：空间坐标变换
- **频域处理**：傅里叶变换相关

#### 4.3.3 应用场景分类
- **工业检测**：质量控制和缺陷检测
- **图像增强**：视觉效果改善
- **目标识别**：物体检测和分类
- **图像分析**：特征提取和测量

### 4.4 插件分组命名规则和添加机制

#### 4.4.1 分组命名规则

**1. 物理目录命名**
```
数字前缀 + 空格 + 英文功能名
例如：
- 1 - Src (数据源)
- 2 - Preprocessings (预处理)
- 3 - Blurs (滤波)
- 4 - Morphology (形态学)
```

**2. 分组接口命名**
```
I + 功能名 + GroupableNodeData
例如：
- IPreprocessingGroupableNodeData (预处理分组接口)
- IMorphologyGroupableNodeData (形态学分组接口)
- IDetectorGroupableNodeData (检测器分组接口)
```

**3. DataGroup类命名**
```
[OpenCV] + 功能名 + DataGroup
例如：
- OpenCVPreprocessingsDataGroup (OpenCV预处理分组)
- FeatureDetectorDataGroup (特征检测分组)
- OpenCVTemplateMatchingDataGroup (OpenCV模板匹配分组)
```

#### 4.4.2 插件添加到分组的步骤

**步骤1：确定目标分组**
```csharp
// 查看现有分组接口
public interface IPreprocessingGroupableNodeData : INodeData, IOrderable { }
public interface IMorphologyGroupableNodeData : INodeData, IOrderable { }
public interface IDetectorGroupableNodeData : INodeData, IOrderable { }
```

**步骤2：实现分组接口**
```csharp
[Icon(FontIcons.YourIcon)]
[Display(Name = "您的插件", GroupName = "基础函数", Description = "插件描述", Order = 10)]
public class YourPluginNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    // 插件实现
}
```

**步骤3：放置到对应目录**
```
Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas\2 - Preprocessings\YourPlugin.cs
```

**步骤4：自动发现和注册**
```csharp
// 系统会自动通过反射发现并注册
protected override IEnumerable<INodeData> CreateNodeDatas()
{
    return this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>()
           .OrderBy(x => x.Order);
}
```

#### 4.4.3 分组映射关系表

| 物理目录 | 分组接口 | DataGroup类 | UI显示名称 | Order范围 |
|----------|----------|-------------|------------|-----------|
| 1 - Src | ISrcImageGroupableNodeData | OpenCVSrcImageDataGroup | 图像源模块 | 10000-10099 |
| 2 - Preprocessings | IPreprocessingGroupableNodeData | OpenCVPreprocessingsDataGroup | 图像预处理模块 | 10100-10199 |
| 3 - Blurs | IBlurGroupableNodeData | BlurDataGroup | 滤波模糊模块 | 10200-10299 |
| 3 - Takeoffs | ITakeoffGroupableNodeData | TakeoffDataGroup | 图像提取模块 | 10300-10399 |
| 4 - Morphology | IMorphologyGroupableNodeData | MorphologyDataGroup | 形态学模块 | 10400-10499 |
| 5 - Conditions | IConditionGroupableNodeData | ConditionDataGroup | 条件判断模块 | 10500-10599 |
| 6 - TemplateMatchings | ITemplateMatchingGroupableNodeData | OpenCVTemplateMatchingDataGroup | 模板匹配模块 | 10600-10699 |
| 7 - Detector | IDetectorGroupableNodeData | DetectorDataGroup | 检测器模块 | 10700-10799 |
| 8 - Feature | IFeatureDetectorOpenCVNodeData | FeatureDetectorDataGroup | 特征识别模块 | 10800-10899 |
| 9 - Other | IOtherGroupableNodeData | OtherDataGroup | 其他功能模块 | 10900-10999 |
| 9 - Outputs | IOutputGroupableNodeData | OutputDataGroup | 输出模块 | 11000-11099 |
| Video | IVideoGroupableNodeData | VideoDataGroup | 视频处理模块 | 11100-11199 |

#### 4.4.4 创建新分组的完整流程

**1. 定义新的分组接口**
```csharp
namespace H.VisionMaster.NodeGroup.Groups.YourNewGroup;

public interface IYourNewGroupGroupableNodeData : INodeData, IOrderable
{
    // 可以添加分组特有的属性或方法
}
```

**2. 创建DataGroup类**
```csharp
[Icon(FontIcons.YourIcon)]
[Display(Name = "您的新模块", Description = "新模块描述", Order = 11200)]
public class YourNewGroupDataGroup : NodeDataGroupBase, IImageDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        return this.GetType().Assembly.GetInstances<IYourNewGroupGroupableNodeData>()
               .OrderBy(x => x.Order);
    }
}
```

**3. 在主程序中注册**
```csharp
// 在DiagramData中添加新分组
protected override IEnumerable<INodeDataGroup> CreateNodeGroups()
{
    return typeof(OpenCVSrcImageDataGroup).GetInstances<IImageDataGroup>()
           .Concat(this.CreateLocalNodeGroups())
           .OrderBy(x => x.Order);
}
```

**4. 创建插件并实现新接口**
```csharp
[Icon(FontIcons.YourIcon)]
[Display(Name = "新插件", GroupName = "新分组", Description = "插件描述", Order = 11201)]
public class YourNewPluginNodeData : OpenCVNodeDataBase, IYourNewGroupGroupableNodeData
{
    protected override FlowableResult<Mat> Invoke(...)
    {
        // 插件实现
    }
}
```

#### 4.4.5 分组的三层架构

**1. 物理层（文件组织）**
- 目录结构：按功能分类的文件夹
- 文件命名：功能相关的类文件
- 作用：代码组织和维护

**2. 逻辑层（接口分组）**
- 分组接口：定义插件归属
- 反射发现：自动扫描和注册
- 作用：运行时插件管理

**3. 展示层（UI分组）**
- Display特性：UI显示配置
- GroupName属性：工具箱分组
- 作用：用户界面组织

#### 4.4.6 扩展性设计

**新分组添加**
1. 定义新的分组接口（继承 INodeData 和 IOrderable）
2. 创建对应的 DataGroup 类
3. 在主程序中注册新的分组
4. 分配合适的 Order 范围（建议间隔100）

**插件迁移**
- 支持插件在不同分组间迁移
- 通过修改实现的接口来改变分组归属
- 保持向后兼容性

**动态分组**
- 支持运行时动态加载新的插件分组
- 通过插件加载器服务管理分组生命周期
- 支持分组的启用/禁用控制

#### 4.4.7 实际插件添加示例

**场景**：为预处理分组添加一个新的"图像锐化"插件

**步骤1：创建插件类文件**
```
文件位置：Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas\2 - Preprocessings\Sharpen.cs
```

**步骤2：实现插件代码**
```csharp
using H.VisionMaster.OpenCV.Base;
using H.VisionMaster.NodeGroup.Groups.Preprocessings;

namespace H.VisionMaster.OpenCV.NodeDatas.Basic;

[Icon(FontIcons.Contrast)]
[Display(Name = "图像锐化", GroupName = "基础函数", Description = "增强图像边缘和细节", Order = 25)]
public class SharpenNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    private double _strength = 1.0;
    [Display(Name = "锐化强度", GroupName = VisionPropertyGroupNames.RunParameters)]
    [Range(0.1, 3.0)]
    public double Strength
    {
        get { return _strength; }
        set
        {
            _strength = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    protected override FlowableResult<Mat> Invoke(
        ISrcVisionNodeData<Mat> srcImageNodeData,
        IVisionNodeData<Mat> from,
        IFlowableDiagramData diagram)
    {
        try
        {
            Mat input = from.Mat;
            if (input?.Empty() != false)
                return this.Error(null, "输入图像无效");

            // 创建锐化核
            Mat kernel = new Mat(3, 3, MatType.CV_32F, new float[]
            {
                0, -1 * (float)Strength, 0,
                -1 * (float)Strength, 1 + 4 * (float)Strength, -1 * (float)Strength,
                0, -1 * (float)Strength, 0
            });

            Mat output = new Mat();
            Cv2.Filter2D(input, output, -1, kernel);

            return this.OK(output, $"锐化处理完成，强度: {Strength:F1}");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"锐化处理失败: {ex.Message}");
        }
    }
}
```

**步骤3：编译和验证**
1. 编译项目，插件会自动被发现
2. 在工具箱的"图像预处理模块"中找到新插件
3. 拖拽到画布进行测试

**结果**：
- 插件自动出现在预处理分组中
- 按Order=25排序在合适位置
- 支持实时参数调整和预览
- 集成到整个处理流程中

**关键点**：
1. **接口实现**：`IPreprocessingGroupableNodeData` 决定分组归属
2. **Order属性**：决定在分组内的显示顺序
3. **GroupName属性**：决定在UI工具箱中的子分组
4. **自动发现**：无需手动注册，编译后自动可用

### 4.5 H.VisionMaster.NodeGroup 模块详解

#### 4.5.1 模块结构和作用

`H.VisionMaster.NodeGroup` 是一个**基础分组定义模块**，它定义了所有插件分组的基础接口和基类：

```
H.VisionMaster.NodeGroup\
├── Groups\                          # 分组定义目录
│   ├── Blurs\                      # 滤波分组
│   │   └── BlurDataGroup.cs        # 滤波分组基类
│   ├── Conditions\                 # 条件分组
│   │   └── ConditionDataGroup.cs   # 条件分组基类
│   ├── Detector\                   # 检测器分组
│   │   └── DetectorDataGroup.cs    # 检测器分组基类
│   ├── Morphologys\                # 形态学分组
│   │   └── MorphologyDataGroup.cs  # 形态学分组基类
│   ├── Others\                     # 其他功能分组
│   │   └── OtherDataGroup.cs       # 其他功能分组基类
│   ├── Outputs\                    # 输出分组
│   │   └── OutputDataGroup.cs      # 输出分组基类
│   ├── Preprocessings\             # 预处理分组
│   │   └── PreprocessingDataGroup.cs # 预处理分组基类
│   ├── SrcImages\                  # 数据源分组
│   │   └── SrcImageDataGroup.cs    # 数据源分组基类
│   ├── Takeoffs\                   # 提取分组
│   │   └── TakeoffDataGroup.cs     # 提取分组基类
│   └── TemplateMatchings\          # 模板匹配分组
│       └── TemplateMatchingDataGroup.cs # 模板匹配分组基类
└── Themes\                         # 主题资源
    └── Generic.xaml                # 通用样式
```

#### 4.5.2 基础分组定义模式

每个分组都遵循相同的定义模式：

**1. 分组接口定义**
```csharp
// 定义分组标记接口
public interface IPreprocessingGroupableNodeData : INodeData, IDisplayBindable
{
    // 标记接口，用于分组识别
}
```

**2. 分组基类定义**
```csharp
[Icon(FontIcons.Color)]
[Display(Name = "图像预处理模块", Description = "对图像进行预处理操作", Order = 10100)]
public class PreprocessingDataGroup : NodeDataGroupBase, IImageDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        // 通过反射自动发现实现了接口的插件
        return this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>()
               .OrderBy(x => x.Order);
    }
}
```

#### 4.5.3 OpenCV项目的继承扩展

OpenCV项目通过**继承基础分组**来扩展功能：

**1. 简单继承模式**（大多数分组）
```csharp
// 直接继承，不添加额外逻辑
public class OpenCVBlurDataGroup : BlurDataGroup, IImageDataGroup
{
    // 继承父类的所有功能，无需重写
}
```

**2. 扩展继承模式**（支持动态插件）
```csharp
public class OpenCVPreprocessingsDataGroup : PreprocessingDataGroup, IImageDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        // 获取静态插件（继承自父类的逻辑）
        var staticPlugins = this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>();

        // 获取动态插件（新增的扩展功能）
        var dynamicPlugins = GetDynamicPlugins();

        // 合并并返回
        return staticPlugins.Concat(dynamicPlugins.Cast<INodeData>())
                           .OrderBy(x => ((IDisplayBindable)x).Order);
    }

    private IEnumerable<IPreprocessingGroupableNodeData> GetDynamicPlugins()
    {
        // 通过插件加载器获取动态加载的插件
        var pluginLoader = Ioc.GetService<IPluginLoaderService>(false);
        return pluginLoader?.GetLoadedPlugins<IPreprocessingGroupableNodeData>() ??
               Enumerable.Empty<IPreprocessingGroupableNodeData>();
    }
}
```

#### 4.5.4 分组注册和发现机制

**1. 分组自动发现**
```csharp
// 在 OpenCVVisionDiagramData 中自动发现所有分组
protected override IEnumerable<INodeDataGroup> CreateNodeGroups()
{
    // 通过反射发现所有实现了 IImageDataGroup 的分组
    return typeof(OpenCVSrcImageDataGroup).GetInstances<IImageDataGroup>()
           .Concat(this.CreateLocalNodeGroups())  // 添加本地分组
           .OrderBy(x => x.Order);                // 按Order排序
}
```

**2. 插件自动发现**
```csharp
// 每个分组内部自动发现对应的插件
protected override IEnumerable<INodeData> CreateNodeDatas()
{
    // 扫描当前程序集中实现了特定接口的所有类
    return this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>()
           .OrderBy(x => x.Order);
}
```

#### 4.5.5 新分组添加的完整流程

**步骤1：在 H.VisionMaster.NodeGroup 中定义基础分组**
```csharp
// 1. 定义分组接口
namespace H.VisionMaster.NodeGroup.Groups.YourNewGroup;

public interface IYourNewGroupGroupableNodeData : INodeData, IDisplayBindable
{
}

// 2. 定义分组基类
[Icon(FontIcons.YourIcon)]
[Display(Name = "您的新模块", Description = "新模块描述", Order = 11200)]
public class YourNewGroupDataGroup : NodeDataGroupBase, IImageDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        return this.GetType().Assembly.GetInstances<IYourNewGroupGroupableNodeData>()
               .OrderBy(x => x.Order);
    }
}
```

**步骤2：在 OpenCV 项目中创建扩展分组**
```csharp
// 在 H.VisionMaster.OpenCV\NodeDataGroups\ 中创建
namespace H.VisionMaster.OpenCV.NodeDataGroups;

public class OpenCVYourNewGroupDataGroup : YourNewGroupDataGroup, IImageDataGroup
{
    // 可以选择直接继承或添加扩展功能
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        // 如果需要支持动态插件，可以重写此方法
        return base.CreateNodeDatas();
    }
}
```

**步骤3：创建插件实现**
```csharp
// 在对应的 NodeDatas 目录中创建插件
[Icon(FontIcons.YourIcon)]
[Display(Name = "新插件", GroupName = "新分组", Description = "插件描述", Order = 11201)]
public class YourNewPluginNodeData : OpenCVNodeDataBase, IYourNewGroupGroupableNodeData
{
    protected override FlowableResult<Mat> Invoke(...)
    {
        // 插件实现
    }
}
```

**步骤4：系统自动集成**
- 编译后，分组会被自动发现并添加到工具箱
- 插件会被自动归类到对应分组
- 无需手动注册或配置

#### 4.5.6 分组层次结构总结

```
基础层 (H.VisionMaster.NodeGroup)
├── 定义分组接口 (IXxxGroupableNodeData)
├── 定义分组基类 (XxxDataGroup)
└── 提供基础的插件发现机制

扩展层 (H.VisionMaster.OpenCV)
├── 继承基础分组 (OpenCVXxxDataGroup)
├── 扩展动态插件支持
└── 实现具体的插件类

应用层 (H.App.VisionMaster.OpenCV)
├── 注册所有分组到主程序
├── 管理分组的生命周期
└── 提供用户界面集成
```

这种**三层架构**的优势：
1. **基础层**提供标准化的分组定义
2. **扩展层**添加特定功能（如动态插件支持）
3. **应用层**负责集成和用户界面
4. **松耦合**：各层独立，易于维护和扩展
5. **自动化**：通过反射实现自动发现和注册

---

## 5. 开发指南

### 5.1 新插件开发流程

#### 步骤1：确定插件分类
1. 分析插件功能，确定所属分组
2. 选择合适的基类（通常是 OpenCVNodeDataBase）
3. 确定需要实现的分组接口

#### 步骤2：创建插件类
```csharp
[Icon(FontIcons.YourIcon)]
[Display(Name = "插件名称", GroupName = "分组名", Description = "功能描述", Order = 1)]
public class YourPluginNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    // 插件实现
}
```

#### 步骤3：实现核心方法
```csharp
protected override FlowableResult<Mat> Invoke(
    ISrcVisionNodeData<Mat> srcImageNodeData, 
    IVisionNodeData<Mat> from, 
    IFlowableDiagramData diagram)
{
    try
    {
        Mat input = from.Mat;
        if (input?.Empty() != false)
            return this.Error(null, "输入图像无效");

        // 您的处理逻辑
        Mat output = ProcessImage(input);

        return this.OK(output, "处理完成");
    }
    catch (Exception ex)
    {
        return this.Error(from?.Mat, $"处理失败: {ex.Message}");
    }
}
```

#### 步骤4：添加参数属性
```csharp
private double _threshold = 128.0;
[Display(Name = "阈值", GroupName = VisionPropertyGroupNames.RunParameters)]
[Range(0.0, 255.0)]
public double Threshold
{
    get { return _threshold; }
    set
    {
        _threshold = value;
        RaisePropertyChanged();
        this.UpdateInvokeCurrent(); // 实时预览
    }
}
```

### 5.2 接口要求

#### 5.2.1 必须实现的接口
- **IOpenCVNodeData**：标记为OpenCV插件
- **分组接口**：如 IPreprocessingGroupableNodeData
- **IDisplayBindable**：支持UI绑定（通过基类继承）

#### 5.2.2 必须实现的方法
- **Invoke()**：核心处理逻辑
- **属性访问器**：参数的 get/set 方法

#### 5.2.3 推荐实现的方法
- **LoadDefault()**：加载默认参数
- **IsValid()**：验证输入有效性

### 5.3 集成方式

#### 5.3.1 静态集成
1. 将插件类添加到对应的目录
2. 确保实现了正确的分组接口
3. 编译后自动被发现和注册

#### 5.3.2 动态集成
1. 创建独立的插件项目
2. 引用 H.VisionMaster.PluginInterface
3. 编译为独立DLL
4. 放置到插件目录，运行时动态加载

### 5.4 最佳实践

#### 5.4.1 错误处理
```csharp
try
{
    // 处理逻辑
    return this.OK(result);
}
catch (OpenCVException ex)
{
    return this.Error(input, $"OpenCV错误: {ex.Message}");
}
catch (Exception ex)
{
    return this.Error(input, $"未知错误: {ex.Message}");
}
```

#### 5.4.2 资源管理
```csharp
protected override FlowableResult<Mat> Invoke(...)
{
    Mat temp = null;
    try
    {
        temp = new Mat();
        // 使用 temp
        return this.OK(temp.Clone());
    }
    finally
    {
        temp?.Dispose();
    }
}
```

#### 5.4.3 参数验证
```csharp
public double Threshold
{
    get { return _threshold; }
    set
    {
        if (value < 0 || value > 255)
            throw new ArgumentOutOfRangeException(nameof(value));
        _threshold = value;
        RaisePropertyChanged();
        this.UpdateInvokeCurrent();
    }
}
```

#### 5.4.4 性能优化
- 避免不必要的内存分配
- 重用临时对象
- 合理使用并行处理
- 及时释放OpenCV资源

---

## 6. 代码示例

### 6.1 简单预处理插件示例

```csharp
using H.VisionMaster.OpenCV.Base;
using H.VisionMaster.NodeGroup.Groups.Preprocessings;

namespace H.VisionMaster.OpenCV.NodeDatas.Basic;

[Icon(FontIcons.Color)]
[Display(Name = "图像灰度化", GroupName = "基础函数", Description = "将彩色图像转换为灰度图像", Order = 1)]
public class GrayscaleNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    protected override FlowableResult<Mat> Invoke(
        ISrcVisionNodeData<Mat> srcImageNodeData, 
        IVisionNodeData<Mat> from, 
        IFlowableDiagramData diagram)
    {
        try
        {
            if (from?.Mat == null || from.Mat.Empty())
                return this.Error(null, "输入图像无效");

            Mat grayMat = new Mat();
            Cv2.CvtColor(from.Mat, grayMat, ColorConversionCodes.BGR2GRAY);
            
            return this.OK(grayMat, "灰度化处理完成");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"灰度化处理失败: {ex.Message}");
        }
    }
}
```

### 6.2 带参数的滤波插件示例

```csharp
[Icon(FontIcons.InPrivate)]
[Display(Name = "高斯滤波", GroupName = "滤波模糊", Description = "高斯核滤波去噪", Order = 1)]
public class GaussianBlurNodeData : OpenCVNodeDataBase, IBlurGroupableNodeData
{
    private System.Windows.Size _ksize = new System.Windows.Size(7, 7);
    [Display(Name = "核大小", GroupName = VisionPropertyGroupNames.RunParameters)]
    public System.Windows.Size KSize
    {
        get { return _ksize; }
        set
        {
            _ksize = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private double _sigmaX = 0;
    [Display(Name = "X方向标准差", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double SigmaX
    {
        get { return _sigmaX; }
        set
        {
            _sigmaX = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    protected override FlowableResult<Mat> Invoke(
        ISrcVisionNodeData<Mat> srcImageNodeData, 
        IVisionNodeData<Mat> from, 
        IFlowableDiagramData diagram)
    {
        try
        {
            Mat input = from.Mat;
            if (input?.Empty() != false)
                return this.Error(null, "输入图像无效");

            Mat output = new Mat();
            Cv2.GaussianBlur(input, output, KSize.ToCVSize(), SigmaX);
            
            return this.OK(output, "高斯滤波完成");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"高斯滤波失败: {ex.Message}");
        }
    }
}
```

### 6.3 特征检测插件示例

```csharp
[Icon(FontIcons.FitPage)]
[Display(Name = "SIFT特征检测", GroupName = "特征检测", Description = "SIFT关键点检测", Order = 1)]
public class SiftFeatureDetectorNodeData : FeatureOpenCVNodeDataBase
{
    private int _nFeatures = 0;
    [Display(Name = "特征点数量", GroupName = VisionPropertyGroupNames.RunParameters)]
    public int NFeatures
    {
        get { return _nFeatures; }
        set
        {
            _nFeatures = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    protected override FlowableResult<Mat> Invoke(
        ISrcVisionNodeData<Mat> srcImageNodeData, 
        IVisionNodeData<Mat> from, 
        IFlowableDiagramData diagram)
    {
        try
        {
            Mat input = from.Mat;
            if (input?.Empty() != false)
                return this.Error(null, "输入图像无效");

            using var sift = SIFT.Create(nFeatures: NFeatures);
            KeyPoint[] keypoints = sift.Detect(input);
            
            // 更新特征点数量结果
            this.FeatureCountResult = keypoints.Length;
            
            // 绘制特征点
            Mat output = new Mat();
            Cv2.DrawKeypoints(input, keypoints, output, Scalar.Red);
            
            return this.OK(output, $"检测到 {keypoints.Length} 个SIFT特征点");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"SIFT特征检测失败: {ex.Message}");
        }
    }
}
```

### 6.4 插件分组注册示例

```csharp
[Icon(FontIcons.Color)]
[Display(Name = "图像预处理模块", Description = "对图像进行预处理操作", Order = 10100)]
public class OpenCVPreprocessingsDataGroup : PreprocessingDataGroup, IImageDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        // 获取静态插件
        var staticPlugins = this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>();
        var staticList = staticPlugins.ToList();

        // 获取动态插件
        var dynamicPlugins = GetDynamicPlugins();
        var dynamicList = dynamicPlugins.ToList();

        // 合并并排序
        var allPlugins = staticList.Concat(dynamicList.Cast<INodeData>())
                                  .OrderBy(x => ((IDisplayBindable)x).Order)
                                  .ToList();
        
        return allPlugins;
    }

    private IEnumerable<IPreprocessingGroupableNodeData> GetDynamicPlugins()
    {
        try
        {
            var pluginLoader = Ioc.GetService<IPluginLoaderService>(false);
            return pluginLoader?.GetLoadedPlugins<IPreprocessingGroupableNodeData>() ?? 
                   Enumerable.Empty<IPreprocessingGroupableNodeData>();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取动态插件失败: {ex.Message}");
            return Enumerable.Empty<IPreprocessingGroupableNodeData>();
        }
    }
}
```

### 6.5 插件加载服务示例

```csharp
public class PluginLoaderService : IPluginLoaderService
{
    private readonly ConcurrentDictionary<string, PluginInfo> _pluginInfos = new();
    private readonly List<IOpenCVNodeData> _loadedPlugins = new();
    private readonly object _lockObject = new();

    public async Task<PluginLoadResult> LoadPluginFromAssemblyAsync(string assemblyPath)
    {
        var result = new PluginLoadResult { IsSuccess = true };

        try
        {
            var assembly = Assembly.LoadFrom(assemblyPath);
            var pluginTypes = assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract)
                .Where(t => typeof(IOpenCVNodeData).IsAssignableFrom(t))
                .ToArray();

            foreach (var pluginType in pluginTypes)
            {
                try
                {
                    var instance = Activator.CreateInstance(pluginType) as IOpenCVNodeData;
                    if (instance != null)
                    {
                        var metadata = instance.GetMetadata();
                        var pluginInfo = new PluginInfo
                        {
                            Name = metadata.Name,
                            Version = metadata.Version,
                            Description = metadata.Description,
                            Author = metadata.Author,
                            AssemblyPath = assemblyPath,
                            PluginType = pluginType
                        };

                        _pluginInfos[metadata.Name] = pluginInfo;
                        
                        lock (_lockObject)
                        {
                            _loadedPlugins.Add(instance);
                        }

                        result.LoadedCount++;
                        result.LoadedPlugins.Add(pluginInfo);

                        PluginLoaded?.Invoke(this, new PluginLoadedEventArgs
                        {
                            PluginInfo = pluginInfo,
                            PluginInstance = instance
                        });
                    }
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    result.ErrorMessages.Add($"创建插件实例失败 {pluginType.Name}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.FailedCount++;
            result.ErrorMessages.Add($"加载程序集失败 {assemblyPath}: {ex.Message}");
        }

        return result;
    }

    public IEnumerable<T> GetLoadedPlugins<T>() where T : class
    {
        lock (_lockObject)
        {
            return _loadedPlugins.OfType<T>().ToList();
        }
    }
}
```

---

## 📝 总结

本文档详细分析了 WPF-VisionMaster 项目中 OpenCV 插件架构的设计原理和实现细节。该架构具有以下特点：

### 优势
1. **模块化设计**：清晰的分层架构和接口定义
2. **自动发现**：基于反射的插件自动注册机制
3. **易于扩展**：标准化的插件开发流程
4. **分类清晰**：基于功能和接口的双重分类体系
5. **支持动态加载**：运行时插件热插拔

### 设计模式应用
- **工厂模式**：插件实例化
- **模板方法模式**：统一的执行流程
- **策略模式**：可替换的算法实现
- **观察者模式**：参数变更通知
- **依赖注入**：服务管理

### 扩展建议
1. 考虑添加插件版本管理机制
2. 增强插件依赖关系处理
3. 添加插件性能监控功能
4. 支持插件配置的持久化
5. 提供更丰富的插件开发工具

该架构为计算机视觉应用提供了一个强大、灵活且易于维护的插件系统基础。

---

## 附录A：插件开发检查清单

### 开发前准备
- [ ] 确定插件功能和分类
- [ ] 选择合适的基类和接口
- [ ] 设计参数和属性结构
- [ ] 准备测试图像数据

### 代码实现
- [ ] 继承正确的基类
- [ ] 实现必要的分组接口
- [ ] 添加Display和Icon特性
- [ ] 实现Invoke核心方法
- [ ] 添加参数属性和验证
- [ ] 实现错误处理机制
- [ ] 添加资源释放逻辑

### 测试验证
- [ ] 单元测试覆盖
- [ ] 集成测试验证
- [ ] 性能测试评估
- [ ] 内存泄漏检查
- [ ] 异常情况处理

### 文档和发布
- [ ] 编写插件说明文档
- [ ] 添加使用示例
- [ ] 版本号和变更日志
- [ ] 打包和部署准备

---

## 附录B：常见问题解答

### Q1: 如何调试插件执行过程？
**A**: 在Invoke方法中添加断点，或使用System.Diagnostics.Debug.WriteLine输出调试信息。

### Q2: 插件参数变更后如何实时预览？
**A**: 在参数的set方法中调用this.UpdateInvokeCurrent()方法。

### Q3: 如何处理OpenCV异常？
**A**: 使用try-catch捕获OpenCVException，并返回this.Error()结果。

### Q4: 插件如何访问全局配置？
**A**: 通过依赖注入获取配置服务，或使用静态配置类。

### Q5: 如何实现插件间的数据传递？
**A**: 通过FlowableResult<Mat>返回处理结果，下游插件通过from参数接收。

---

## 附录C：性能优化建议

### 内存管理
1. **及时释放Mat对象**：使用using语句或手动调用Dispose()
2. **避免频繁分配**：重用临时对象和缓冲区
3. **控制图像尺寸**：对大图像进行适当缩放

### 算法优化
1. **选择合适的数据类型**：根据精度要求选择CV_8U、CV_32F等
2. **利用OpenCV并行化**：启用TBB或OpenMP支持
3. **缓存计算结果**：对重复计算进行缓存

### UI响应性
1. **异步处理**：长时间操作使用异步方法
2. **进度反馈**：提供处理进度信息
3. **取消机制**：支持用户取消长时间操作

---

## 附录D：版本兼容性

### OpenCV版本支持
- **当前版本**：OpenCVSharp 4.x
- **最低要求**：OpenCV 4.0+
- **推荐版本**：OpenCV 4.5+

### .NET版本支持
- **目标框架**：.NET 6.0+
- **最低要求**：.NET Framework 4.8
- **推荐版本**：.NET 8.0

### 依赖库版本
- **WPF框架**：Windows Presentation Foundation
- **OpenCVSharp**：4.5.0+
- **System.ComponentModel.DataAnnotations**：6.0.0+

---

## 附录E：插件分发和部署

### 静态插件部署
1. 将插件源码添加到对应目录
2. 编译整个解决方案
3. 插件自动集成到主程序

### 动态插件部署
1. 创建独立插件项目
2. 引用必要的接口库
3. 编译为独立DLL
4. 复制到插件目录
5. 重启应用或热加载

### 插件打包规范
```
PluginName.dll              # 主插件程序集
PluginName.pdb              # 调试符号文件
PluginName.deps.json        # 依赖清单
PluginName.xml              # XML文档
README.md                   # 插件说明
LICENSE                     # 许可证文件
```

### 插件元数据文件
```json
{
  "name": "插件名称",
  "version": "1.0.0",
  "description": "插件描述",
  "author": "作者名称",
  "category": "插件分类",
  "dependencies": [
    {
      "name": "OpenCVSharp4",
      "version": "4.5.0"
    }
  ],
  "supportedPlatforms": ["Windows"],
  "minimumFrameworkVersion": "net6.0"
}
```

---

## 结语

本技术文档全面分析了 WPF-VisionMaster 项目的 OpenCV 插件架构，从项目结构到具体实现，从设计原理到开发实践，为开发者提供了完整的技术参考。

该插件架构体现了现代软件工程的最佳实践，通过合理的抽象设计、清晰的接口定义和灵活的扩展机制，为计算机视觉应用开发提供了强大的技术基础。

希望本文档能够帮助开发者更好地理解和使用这个插件系统，并在此基础上开发出更多优秀的计算机视觉应用。
