
// Copyright (c) HeBianGu Authors. All Rights Reserved.
// Author: HeBianGu
// Github: https://github.com/HeBianGu/WPF-Control
// Document: https://hebiangu.github.io/WPF-Control-Docs
// QQ:908293466 Group:971261058
// bilibili: https://space.bilibili.com/370266611
// Licensed under the MIT License (the "License")

using System;
using System.Collections.Generic;
using System.Linq;

namespace H.VisionMaster.NodeGroup;

/// <summary>
/// 插件分组名称常量类
/// 用于标准化插件的 GroupName 属性，防止拼写错误导致插件无法正确分组
/// </summary>
public static class VisionPluginGroupNames
{
    #region 基础功能分组
    /// <summary>基础函数 - 最常用的基础图像处理功能</summary>
    public const string BasicFunctions = "基础函数";
    
    /// <summary>高级函数 - 复杂的图像处理算法</summary>
    public const string AdvancedFunctions = "高级函数";
    
    /// <summary>实用工具 - 辅助工具和实用功能</summary>
    public const string Utilities = "实用工具";
    #endregion

    #region 图像预处理分组
    /// <summary>颜色转换 - RGB、HSV、灰度等颜色空间转换</summary>
    public const string ColorConversion = "颜色转换";
    
    /// <summary>几何变换 - 旋转、缩放、平移等几何操作</summary>
    public const string GeometricTransform = "几何变换";
    
    /// <summary>图像增强 - 对比度、亮度、锐化等增强操作</summary>
    public const string ImageEnhancement = "图像增强";
    
    /// <summary>噪声处理 - 去噪、平滑等噪声处理</summary>
    public const string NoiseProcessing = "噪声处理";
    #endregion

    #region 滤波和模糊分组
    /// <summary>线性滤波 - 均值、高斯等线性滤波器</summary>
    public const string LinearFilters = "线性滤波";
    
    /// <summary>非线性滤波 - 中值、双边等非线性滤波器</summary>
    public const string NonLinearFilters = "非线性滤波";
    
    /// <summary>边缘检测 - Sobel、Canny等边缘检测算法</summary>
    public const string EdgeDetection = "边缘检测";
    #endregion

    #region 形态学操作分组
    /// <summary>基本形态学 - 腐蚀、膨胀等基本操作</summary>
    public const string BasicMorphology = "基本形态学";
    
    /// <summary>复合形态学 - 开运算、闭运算等复合操作</summary>
    public const string CompoundMorphology = "复合形态学";
    
    /// <summary>形态学梯度 - 梯度、顶帽、黑帽等操作</summary>
    public const string MorphologyGradient = "形态学梯度";
    #endregion

    #region 特征检测分组
    /// <summary>角点检测 - Harris、FAST等角点检测算法</summary>
    public const string CornerDetection = "角点检测";
    
    /// <summary>特征描述子 - SIFT、SURF、ORB等特征描述</summary>
    public const string FeatureDescriptors = "特征描述子";
    
    /// <summary>特征匹配 - 特征点匹配和验证</summary>
    public const string FeatureMatching = "特征匹配";
    #endregion

    #region 对象检测分组
    /// <summary>轮廓检测 - 轮廓查找和分析</summary>
    public const string ContourDetection = "轮廓检测";
    
    /// <summary>形状分析 - 几何形状识别和分析</summary>
    public const string ShapeAnalysis = "形状分析";
    
    /// <summary>目标跟踪 - 对象跟踪算法</summary>
    public const string ObjectTracking = "目标跟踪";
    #endregion

    #region 模板匹配分组
    /// <summary>模板匹配 - 基础模板匹配算法</summary>
    public const string TemplateMatching = "模板匹配";
    
    /// <summary>多尺度匹配 - 多尺度模板匹配</summary>
    public const string MultiScaleMatching = "多尺度匹配";
    
    /// <summary>旋转匹配 - 旋转不变模板匹配</summary>
    public const string RotationMatching = "旋转匹配";
    #endregion

    #region 机器学习分组
    /// <summary>深度学习 - 神经网络相关功能</summary>
    public const string DeepLearning = "深度学习";
    
    /// <summary>传统机器学习 - SVM、KMeans等传统算法</summary>
    public const string MachineLearning = "传统机器学习";
    
    /// <summary>分类器 - 各种分类算法</summary>
    public const string Classifiers = "分类器";
    #endregion

    #region 测量和分析分组
    /// <summary>尺寸测量 - 长度、面积、角度等测量</summary>
    public const string Measurement = "尺寸测量";
    
    /// <summary>统计分析 - 直方图、统计特征等分析</summary>
    public const string StatisticalAnalysis = "统计分析";
    
    /// <summary>质量检测 - 缺陷检测、质量评估</summary>
    public const string QualityInspection = "质量检测";
    #endregion

    #region 输入输出分组
    /// <summary>图像输入 - 各种图像输入源</summary>
    public const string ImageInput = "图像输入";
    
    /// <summary>图像输出 - 图像保存和输出</summary>
    public const string ImageOutput = "图像输出";
    
    /// <summary>数据输出 - 结果数据输出</summary>
    public const string DataOutput = "数据输出";
    #endregion

    #region 条件控制分组
    /// <summary>逻辑判断 - 条件判断和逻辑控制</summary>
    public const string LogicalOperations = "逻辑判断";
    
    /// <summary>流程控制 - 分支、循环等流程控制</summary>
    public const string FlowControl = "流程控制";
    
    /// <summary>数据转换 - 数据类型转换和处理</summary>
    public const string DataConversion = "数据转换";
    #endregion

    #region 视频处理分组
    /// <summary>视频输入 - 视频文件和摄像头输入</summary>
    public const string VideoInput = "视频输入";
    
    /// <summary>视频处理 - 视频帧处理和分析</summary>
    public const string VideoProcessing = "视频处理";
    
    /// <summary>视频输出 - 视频保存和输出</summary>
    public const string VideoOutput = "视频输出";
    #endregion

    #region 其他功能分组
    /// <summary>调试工具 - 调试和诊断工具</summary>
    public const string DebugTools = "调试工具";
    
    /// <summary>性能优化 - 性能监控和优化工具</summary>
    public const string Performance = "性能优化";
    
    /// <summary>扩展功能 - 其他扩展功能</summary>
    public const string Extensions = "扩展功能";
    #endregion

    /// <summary>
    /// 获取所有分组名称
    /// </summary>
    /// <returns>所有分组名称的集合</returns>
    public static IEnumerable<string> GetAllGroupNames()
    {
        return typeof(VisionPluginGroupNames)
            .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
            .Where(f => f.FieldType == typeof(string))
            .Select(f => f.GetValue(null) as string)
            .Where(value => !string.IsNullOrEmpty(value))
            .OrderBy(name => name);
    }

    /// <summary>
    /// 验证分组名称是否有效
    /// </summary>
    /// <param name="groupName">要验证的分组名称</param>
    /// <returns>如果分组名称有效返回true，否则返回false</returns>
    public static bool IsValidGroupName(string groupName)
    {
        if (string.IsNullOrEmpty(groupName))
            return false;
            
        return GetAllGroupNames().Contains(groupName);
    }

    /// <summary>
    /// 获取推荐的分组名称（基于输入的模糊匹配）
    /// </summary>
    /// <param name="input">输入的分组名称</param>
    /// <returns>推荐的分组名称列表</returns>
    public static IEnumerable<string> GetSuggestedGroupNames(string input)
    {
        if (string.IsNullOrEmpty(input))
            return GetAllGroupNames();

        var allNames = GetAllGroupNames().ToList();
        
        // 精确匹配
        var exactMatch = allNames.Where(name => 
            string.Equals(name, input, StringComparison.OrdinalIgnoreCase)).ToList();
        
        if (exactMatch.Any())
            return exactMatch;

        // 包含匹配
        var containsMatch = allNames.Where(name => 
            name.Contains(input, StringComparison.OrdinalIgnoreCase) ||
            input.Contains(name, StringComparison.OrdinalIgnoreCase)).ToList();
            
        return containsMatch.Any() ? containsMatch : allNames.Take(5);
    }
}
