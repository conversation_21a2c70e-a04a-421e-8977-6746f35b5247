
// Copyright (c) HeBianGu Authors. All Rights Reserved.
// Author: HeBianGu
// Github: https://github.com/HeBianGu/WPF-Control
// Document: https://hebiangu.github.io/WPF-Control-Docs
// QQ:908293466 Group:971261058
// bilibili: https://space.bilibili.com/370266611
// Licensed under the MIT License (the "License")

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;

namespace H.VisionMaster.NodeGroup;

/// <summary>
/// 插件分组辅助类
/// 提供插件分组相关的实用方法和验证功能
/// </summary>
public static class VisionPluginGroupHelper
{
    /// <summary>
    /// 根据枚举类型获取分组名称
    /// </summary>
    /// <param name="groupType">分组类型枚举</param>
    /// <returns>分组名称字符串</returns>
    public static string GetGroupName(VisionPluginGroupType groupType)
    {
        return groupType.GetDisplayName();
    }

    /// <summary>
    /// 验证插件类的分组配置是否正确
    /// </summary>
    /// <param name="pluginType">插件类型</param>
    /// <returns>验证结果</returns>
    public static PluginGroupValidationResult ValidatePluginGroup(Type pluginType)
    {
        var result = new PluginGroupValidationResult { IsValid = true };

        // 检查是否有Display特性
        var displayAttribute = pluginType.GetCustomAttribute<DisplayAttribute>();
        if (displayAttribute == null)
        {
            result.IsValid = false;
            result.ErrorMessage = "插件类缺少 [Display] 特性";
            return result;
        }

        // 检查GroupName是否设置
        if (string.IsNullOrEmpty(displayAttribute.GroupName))
        {
            result.IsValid = false;
            result.ErrorMessage = "Display 特性中未设置 GroupName 属性";
            return result;
        }

        // 验证GroupName是否为有效的分组名称
        if (!VisionPluginGroupNames.IsValidGroupName(displayAttribute.GroupName))
        {
            result.IsValid = false;
            result.ErrorMessage = $"无效的分组名称: '{displayAttribute.GroupName}'";
            
            // 提供建议的分组名称
            var suggestions = VisionPluginGroupNames.GetSuggestedGroupNames(displayAttribute.GroupName);
            result.SuggestedGroupNames = suggestions.ToList();
        }

        result.CurrentGroupName = displayAttribute.GroupName;
        return result;
    }

    /// <summary>
    /// 获取推荐的分组名称（基于插件名称和功能）
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="pluginDescription">插件描述</param>
    /// <returns>推荐的分组名称列表</returns>
    public static IEnumerable<string> GetRecommendedGroups(string pluginName, string pluginDescription = "")
    {
        var recommendations = new List<string>();
        var searchText = $"{pluginName} {pluginDescription}".ToLower();

        // 基于关键词的推荐逻辑
        var keywordMappings = new Dictionary<string[], string>
        {
            // 基础功能
            { new[] { "灰度", "二值", "阈值", "反转", "翻转" }, VisionPluginGroupNames.BasicFunctions },
            { new[] { "resize", "缩放", "旋转", "平移", "仿射", "透视" }, VisionPluginGroupNames.GeometricTransform },
            { new[] { "亮度", "对比度", "锐化", "增强" }, VisionPluginGroupNames.ImageEnhancement },
            
            // 滤波
            { new[] { "blur", "模糊", "高斯", "均值", "滤波" }, VisionPluginGroupNames.LinearFilters },
            { new[] { "中值", "双边", "非线性" }, VisionPluginGroupNames.NonLinearFilters },
            { new[] { "边缘", "sobel", "canny", "laplacian" }, VisionPluginGroupNames.EdgeDetection },
            
            // 形态学
            { new[] { "腐蚀", "膨胀", "erode", "dilate" }, VisionPluginGroupNames.BasicMorphology },
            { new[] { "开运算", "闭运算", "opening", "closing" }, VisionPluginGroupNames.CompoundMorphology },
            
            // 特征检测
            { new[] { "角点", "harris", "fast", "corner" }, VisionPluginGroupNames.CornerDetection },
            { new[] { "sift", "surf", "orb", "特征点" }, VisionPluginGroupNames.FeatureDescriptors },
            
            // 检测
            { new[] { "轮廓", "contour", "findcontours" }, VisionPluginGroupNames.ContourDetection },
            { new[] { "圆", "直线", "矩形", "形状" }, VisionPluginGroupNames.ShapeAnalysis },
            
            // 模板匹配
            { new[] { "模板", "匹配", "template", "match" }, VisionPluginGroupNames.TemplateMatching },
            
            // 机器学习
            { new[] { "神经网络", "深度学习", "dnn", "neural" }, VisionPluginGroupNames.DeepLearning },
            { new[] { "分类", "classifier", "svm", "kmeans" }, VisionPluginGroupNames.MachineLearning },
            
            // 测量
            { new[] { "测量", "长度", "面积", "角度", "距离" }, VisionPluginGroupNames.Measurement },
            { new[] { "直方图", "统计", "histogram", "stats" }, VisionPluginGroupNames.StatisticalAnalysis },
            
            // 输入输出
            { new[] { "读取", "加载", "输入", "load", "read" }, VisionPluginGroupNames.ImageInput },
            { new[] { "保存", "输出", "save", "write" }, VisionPluginGroupNames.ImageOutput },
            
            // 条件控制
            { new[] { "判断", "条件", "if", "比较" }, VisionPluginGroupNames.LogicalOperations },
            { new[] { "循环", "分支", "控制", "flow" }, VisionPluginGroupNames.FlowControl },
            
            // 视频
            { new[] { "视频", "摄像头", "video", "camera" }, VisionPluginGroupNames.VideoInput },
            
            // 其他
            { new[] { "调试", "debug", "测试" }, VisionPluginGroupNames.DebugTools },
            { new[] { "性能", "优化", "performance" }, VisionPluginGroupNames.Performance }
        };

        // 查找匹配的关键词
        foreach (var mapping in keywordMappings)
        {
            if (mapping.Key.Any(keyword => searchText.Contains(keyword)))
            {
                recommendations.Add(mapping.Value);
            }
        }

        // 如果没有找到匹配的，返回最常用的分组
        if (!recommendations.Any())
        {
            recommendations.AddRange(new[]
            {
                VisionPluginGroupNames.BasicFunctions,
                VisionPluginGroupNames.AdvancedFunctions,
                VisionPluginGroupNames.Utilities
            });
        }

        return recommendations.Distinct();
    }

    /// <summary>
    /// 生成插件Display特性的代码片段
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="groupType">分组类型</param>
    /// <param name="description">插件描述</param>
    /// <param name="order">排序号</param>
    /// <returns>Display特性的代码字符串</returns>
    public static string GenerateDisplayAttributeCode(
        string pluginName, 
        VisionPluginGroupType groupType, 
        string description = "", 
        int order = 1)
    {
        var groupName = GetGroupName(groupType);
        var desc = string.IsNullOrEmpty(description) ? pluginName : description;
        
        return $"[Display(Name = \"{pluginName}\", GroupName = VisionPluginGroupNames.{groupType}, Description = \"{desc}\", Order = {order})]";
    }

    /// <summary>
    /// 生成插件Display特性的代码片段（使用字符串分组名）
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="groupName">分组名称</param>
    /// <param name="description">插件描述</param>
    /// <param name="order">排序号</param>
    /// <returns>Display特性的代码字符串</returns>
    public static string GenerateDisplayAttributeCode(
        string pluginName, 
        string groupName, 
        string description = "", 
        int order = 1)
    {
        var desc = string.IsNullOrEmpty(description) ? pluginName : description;
        
        return $"[Display(Name = \"{pluginName}\", GroupName = \"{groupName}\", Description = \"{desc}\", Order = {order})]";
    }

    /// <summary>
    /// 获取所有可用的分组信息
    /// </summary>
    /// <returns>分组信息列表</returns>
    public static IEnumerable<GroupInfo> GetAllGroupInfo()
    {
        return Enum.GetValues<VisionPluginGroupType>()
                   .Select(groupType => new GroupInfo
                   {
                       EnumValue = groupType,
                       Name = groupType.GetDisplayName(),
                       Description = groupType.GetDescription(),
                       ConstantName = groupType.ToString()
                   })
                   .OrderBy(info => info.Name);
    }
}

/// <summary>
/// 插件分组验证结果
/// </summary>
public class PluginGroupValidationResult
{
    /// <summary>验证是否通过</summary>
    public bool IsValid { get; set; }
    
    /// <summary>错误消息</summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>当前分组名称</summary>
    public string CurrentGroupName { get; set; } = string.Empty;
    
    /// <summary>建议的分组名称列表</summary>
    public List<string> SuggestedGroupNames { get; set; } = new();
}

/// <summary>
/// 分组信息
/// </summary>
public class GroupInfo
{
    /// <summary>枚举值</summary>
    public VisionPluginGroupType EnumValue { get; set; }
    
    /// <summary>显示名称</summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>描述</summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>常量名称</summary>
    public string ConstantName { get; set; } = string.Empty;
}
