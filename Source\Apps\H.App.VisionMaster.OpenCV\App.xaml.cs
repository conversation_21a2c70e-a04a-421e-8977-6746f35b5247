﻿using H.App.VisionMaster.OpenCV.Projects;
using H.Extensions.ApplicationBase;
using H.Iocable;
using H.Themes.Colors;
using H.VisionMaster.NodeData;
using H.VisionMaster.PluginInterface.Services;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using System.Threading.Tasks;
using System.Windows;

namespace H.App.VisionMaster.OpenCV;
/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : ApplicationBase
{
    //protected override void OnExcetion()
    //{
    //    //base.OnExcetion();
    //}
    protected override void ConfigureServices(IServiceCollection services)
    {
        base.ConfigureServices(services);
        services.AddApplicationServices();
        services.AddProject<VisionProjectService>(x =>
        {
            x.Extenstion = ".json";
            x.JsonSerializerService = new NewtonsoftJsonSerializerService();
        });

        // 注册插件服务
        services.AddSingleton<IPluginLoaderService, PluginLoaderService>();
    }

    protected override void Configure(IApplicationBuilder app)
    {
        base.Configure(app);
        //app.UseTheme(x => x.ColorResource = x.ColorResources.OfType<TechnologyBlueDarkColorResource>().FirstOrDefault());
        //app.UseAdorner();
        app.UseSplashScreenOptions(x =>
        {
            x.ProductFontSize = 55;
            x.Product = "H Vision Master";
            x.Sub = "OpenCV2.0版本";
        });
        app.UseApplicationOptions(x =>
        x.UseThemeModuleOptions(x =>
        {
            x.UseColorThemeOptions(x =>
            {
                x.ColorResource = x.ColorResources.OfType<DarkColorResource>().FirstOrDefault();
            });
        }));

        app.UseSettingDataOptions(x =>
        {
            x.Add(VisionSettings.Instance);
        });
    }

    protected override System.Windows.Window CreateMainWindow(StartupEventArgs e)
    {
        // 异步加载插件
        _ = Task.Run(async () =>
        {
            try
            {
                var pluginLoader = Ioc.GetService<IPluginLoaderService>();
                if (pluginLoader != null)
                {
                    var pluginDirectory = AppDomain.CurrentDomain.BaseDirectory;
                    if (Directory.Exists(pluginDirectory))
                    {
                        var result = await pluginLoader.LoadPluginsFromDirectoryAsync(pluginDirectory);

                        // 在UI线程上显示加载结果
                        Dispatcher.Invoke(() =>
                        {
                            if (result.IsSuccess)
                            {
                                System.Diagnostics.Debug.WriteLine($"成功加载 {result.LoadedCount} 个插件");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"插件加载失败: {string.Join(", ", result.ErrorMessages)}");
                            }
                        });
                    }
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插件加载异常: {ex.Message}");
            }
        });

        return new MainWindow();
    }
}
