

global using H.Extensions.Common;
using H.Controls.Diagram.Datas;
using H.Iocable;
using H.VisionMaster.NodeGroup.Groups.Preprocessings;
using H.VisionMaster.PluginInterface.Services;
using System;
using System.Linq;

namespace H.VisionMaster.OpenCV.NodeDataGroups;

public class OpenCVPreprocessingsDataGroup : PreprocessingDataGroup, IImageDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        // 获取原有的静态插件
        var staticPlugins = this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>();
        var staticList = staticPlugins.ToList();
        System.Diagnostics.Debug.WriteLine($"[调试] 找到 {staticList.Count} 个静态预处理插件");

        // 获取动态加载的插件
        var dynamicPlugins = GetDynamicPlugins();
        var dynamicList = dynamicPlugins.ToList();
        System.Diagnostics.Debug.WriteLine($"[调试] 找到 {dynamicList.Count} 个动态预处理插件");

        // 合并并排序
        var allPlugins = staticList.Concat(dynamicList.Cast<INodeData>()).OrderBy(x => ((IDisplayBindable)x).Order).ToList();
        System.Diagnostics.Debug.WriteLine($"[调试] 总共返回 {allPlugins.Count} 个预处理插件到工具箱");

        return allPlugins;
    }

    private IEnumerable<IPreprocessingGroupableNodeData> GetDynamicPlugins()
    {
        try
        {
            var pluginLoader = Ioc.GetService<IPluginLoaderService>(false);
            if (pluginLoader != null)
            {
                var allLoadedPlugins = pluginLoader.LoadedPlugins;
                System.Diagnostics.Debug.WriteLine($"[调试] 插件加载器中总共有 {allLoadedPlugins.Count} 个插件");

                var preprocessingPlugins = pluginLoader.GetLoadedPlugins<IPreprocessingGroupableNodeData>();
                var pluginList = preprocessingPlugins.ToList();
                System.Diagnostics.Debug.WriteLine($"[调试] 找到 {pluginList.Count} 个预处理插件");

                foreach (var plugin in pluginList)
                {
                    System.Diagnostics.Debug.WriteLine($"[调试] 预处理插件: {plugin.GetType().Name}");
                }

                return pluginList;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[调试] 插件加载器服务未找到");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[调试] 获取动态插件失败: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"[调试] 异常堆栈: {ex.StackTrace}");
        }

        return Enumerable.Empty<IPreprocessingGroupableNodeData>();
    }
}
