
// Copyright (c) HeBianGu Authors. All Rights Reserved.
// Author: HeBianGu
// Github: https://github.com/HeBianGu/WPF-Control
// Document: https://hebiangu.github.io/WPF-Control-Docs
// QQ:908293466 Group:971261058
// bilibili: https://space.bilibili.com/370266611
// Licensed under the MIT License (the "License")

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace H.VisionMaster.NodeGroup;

/// <summary>
/// 插件分组类型枚举
/// 提供强类型的分组选择，配合 VisionPluginGroupNames 常量类使用
/// </summary>
public enum VisionPluginGroupType
{
    #region 基础功能分组
    [Display(Name = "基础函数", Description = "最常用的基础图像处理功能")]
    BasicFunctions,
    
    [Display(Name = "高级函数", Description = "复杂的图像处理算法")]
    AdvancedFunctions,
    
    [Display(Name = "实用工具", Description = "辅助工具和实用功能")]
    Utilities,
    #endregion

    #region 图像预处理分组
    [Display(Name = "颜色转换", Description = "RGB、HSV、灰度等颜色空间转换")]
    ColorConversion,
    
    [Display(Name = "几何变换", Description = "旋转、缩放、平移等几何操作")]
    GeometricTransform,
    
    [Display(Name = "图像增强", Description = "对比度、亮度、锐化等增强操作")]
    ImageEnhancement,
    
    [Display(Name = "噪声处理", Description = "去噪、平滑等噪声处理")]
    NoiseProcessing,
    #endregion

    #region 滤波和模糊分组
    [Display(Name = "线性滤波", Description = "均值、高斯等线性滤波器")]
    LinearFilters,
    
    [Display(Name = "非线性滤波", Description = "中值、双边等非线性滤波器")]
    NonLinearFilters,
    
    [Display(Name = "边缘检测", Description = "Sobel、Canny等边缘检测算法")]
    EdgeDetection,
    #endregion

    #region 形态学操作分组
    [Display(Name = "基本形态学", Description = "腐蚀、膨胀等基本操作")]
    BasicMorphology,
    
    [Display(Name = "复合形态学", Description = "开运算、闭运算等复合操作")]
    CompoundMorphology,
    
    [Display(Name = "形态学梯度", Description = "梯度、顶帽、黑帽等操作")]
    MorphologyGradient,
    #endregion

    #region 特征检测分组
    [Display(Name = "角点检测", Description = "Harris、FAST等角点检测算法")]
    CornerDetection,
    
    [Display(Name = "特征描述子", Description = "SIFT、SURF、ORB等特征描述")]
    FeatureDescriptors,
    
    [Display(Name = "特征匹配", Description = "特征点匹配和验证")]
    FeatureMatching,
    #endregion

    #region 对象检测分组
    [Display(Name = "轮廓检测", Description = "轮廓查找和分析")]
    ContourDetection,
    
    [Display(Name = "形状分析", Description = "几何形状识别和分析")]
    ShapeAnalysis,
    
    [Display(Name = "目标跟踪", Description = "对象跟踪算法")]
    ObjectTracking,
    #endregion

    #region 模板匹配分组
    [Display(Name = "模板匹配", Description = "基础模板匹配算法")]
    TemplateMatching,
    
    [Display(Name = "多尺度匹配", Description = "多尺度模板匹配")]
    MultiScaleMatching,
    
    [Display(Name = "旋转匹配", Description = "旋转不变模板匹配")]
    RotationMatching,
    #endregion

    #region 机器学习分组
    [Display(Name = "深度学习", Description = "神经网络相关功能")]
    DeepLearning,
    
    [Display(Name = "传统机器学习", Description = "SVM、KMeans等传统算法")]
    MachineLearning,
    
    [Display(Name = "分类器", Description = "各种分类算法")]
    Classifiers,
    #endregion

    #region 测量和分析分组
    [Display(Name = "尺寸测量", Description = "长度、面积、角度等测量")]
    Measurement,
    
    [Display(Name = "统计分析", Description = "直方图、统计特征等分析")]
    StatisticalAnalysis,
    
    [Display(Name = "质量检测", Description = "缺陷检测、质量评估")]
    QualityInspection,
    #endregion

    #region 输入输出分组
    [Display(Name = "图像输入", Description = "各种图像输入源")]
    ImageInput,
    
    [Display(Name = "图像输出", Description = "图像保存和输出")]
    ImageOutput,
    
    [Display(Name = "数据输出", Description = "结果数据输出")]
    DataOutput,
    #endregion

    #region 条件控制分组
    [Display(Name = "逻辑判断", Description = "条件判断和逻辑控制")]
    LogicalOperations,
    
    [Display(Name = "流程控制", Description = "分支、循环等流程控制")]
    FlowControl,
    
    [Display(Name = "数据转换", Description = "数据类型转换和处理")]
    DataConversion,
    #endregion

    #region 视频处理分组
    [Display(Name = "视频输入", Description = "视频文件和摄像头输入")]
    VideoInput,
    
    [Display(Name = "视频处理", Description = "视频帧处理和分析")]
    VideoProcessing,
    
    [Display(Name = "视频输出", Description = "视频保存和输出")]
    VideoOutput,
    #endregion

    #region 其他功能分组
    [Display(Name = "调试工具", Description = "调试和诊断工具")]
    DebugTools,
    
    [Display(Name = "性能优化", Description = "性能监控和优化工具")]
    Performance,
    
    [Display(Name = "扩展功能", Description = "其他扩展功能")]
    Extensions
    #endregion
}

/// <summary>
/// VisionPluginGroupType 枚举的扩展方法
/// </summary>
public static class VisionPluginGroupTypeExtensions
{
    /// <summary>
    /// 获取枚举值对应的显示名称
    /// </summary>
    /// <param name="groupType">分组类型枚举值</param>
    /// <returns>显示名称</returns>
    public static string GetDisplayName(this VisionPluginGroupType groupType)
    {
        var field = groupType.GetType().GetField(groupType.ToString());
        var displayAttribute = field?.GetCustomAttributes(typeof(DisplayAttribute), false)
                                     .FirstOrDefault() as DisplayAttribute;
        return displayAttribute?.Name ?? groupType.ToString();
    }

    /// <summary>
    /// 获取枚举值对应的描述
    /// </summary>
    /// <param name="groupType">分组类型枚举值</param>
    /// <returns>描述信息</returns>
    public static string GetDescription(this VisionPluginGroupType groupType)
    {
        var field = groupType.GetType().GetField(groupType.ToString());
        var displayAttribute = field?.GetCustomAttributes(typeof(DisplayAttribute), false)
                                     .FirstOrDefault() as DisplayAttribute;
        return displayAttribute?.Description ?? string.Empty;
    }

    /// <summary>
    /// 从显示名称获取对应的枚举值
    /// </summary>
    /// <param name="displayName">显示名称</param>
    /// <returns>对应的枚举值，如果未找到则返回null</returns>
    public static VisionPluginGroupType? GetGroupTypeByDisplayName(string displayName)
    {
        if (string.IsNullOrEmpty(displayName))
            return null;

        var enumValues = Enum.GetValues<VisionPluginGroupType>();
        return enumValues.FirstOrDefault(e => e.GetDisplayName() == displayName);
    }

    /// <summary>
    /// 获取所有分组类型的显示名称
    /// </summary>
    /// <returns>所有显示名称的集合</returns>
    public static IEnumerable<string> GetAllDisplayNames()
    {
        return Enum.GetValues<VisionPluginGroupType>()
                   .Select(e => e.GetDisplayName())
                   .OrderBy(name => name);
    }
}
